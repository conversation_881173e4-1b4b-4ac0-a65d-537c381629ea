import './bulk-middleware';

import { db } from '@/lib';
import { SCHEMAS } from '@/lib/schemas';
import { SchemaFactory } from '@/schemas/schema-factory';

import { AccountSchema } from './accounts';
import { CalendarEventSchema } from './calendar-event';
import { CustomerSchema } from './customers';
import { CustomReportSchema } from './custom-reports';
import { DeadLeadReasonSchema } from './dead-lead-reasons';
import { FileSchema } from './files';
import { InsuranceCompanySchema } from './insurance-companies';
import { LocationSchema } from './locations';
import { NotificationSchema } from './notifications';
import { ProjectSchema } from './projects';
import { QuoteTemplateSchema } from './quote-template';
import { ScheduledTaskSchema } from './scheduled-tasks';
import { SearchSchema } from './search';
import { ServiceItemSchema } from './service-items';
import { ServiceItemIntegrationSchema } from './service-items-integration';
import { SessionSchema } from './sessions';
import { SubcontractorSchema } from './subcontractor';
import { SystemSchema } from './system';
import { UserSchema } from './users';
import { CrewSchema } from './crews';
import { LeadSourceSchema } from './lead-sources';
import { CustomerIntegrationSchema } from './customer-integration';
import { ProjectInvoiceIntegrationSchema } from './project-invoice-integration';
import { GlobalTargetSchema } from './global-targets';
import { UserTargetSchema } from './user-targets';
import { SubscriptionSchema } from './subscriptions';

db().then(function () {
  AccountSchema.init();
  CalendarEventSchema.init();
  CustomerSchema.init();
  CustomReportSchema.init();
  FileSchema.init();
  InsuranceCompanySchema.init();
  LocationSchema.init();
  NotificationSchema.init();
  ProjectSchema.init();
  QuoteTemplateSchema.init();
  ScheduledTaskSchema.init();
  SearchSchema.init();
  ServiceItemSchema.init();
  SessionSchema.init();
  SubcontractorSchema.init();
  SystemSchema.init();
  UserSchema.init();
  CrewSchema.init();
  DeadLeadReasonSchema.init();
  LeadSourceSchema.init();
  ServiceItemIntegrationSchema.init();
  CustomerIntegrationSchema.init();
  ProjectInvoiceIntegrationSchema.init();
  GlobalTargetSchema.init();
  UserTargetSchema.init();
  SubscriptionSchema.init();
});

export const schemas = {
  entries: SchemaFactory.entries,
  fromCollection: SchemaFactory.fromCollection,
};

export { SCHEMAS };

export * from './accounts';
export * from './addresses';
export * from './calendar-event';
export * from './customers';
export * from './customer-integration';
export * from './custom-reports';
export * from './files';
export * from './insurance-companies';
export * from './locations';
export * from './notifications';
export * from './projects';
export * from './quote-template';
export * from './scheduled-tasks';
export * from './search';
export * from './service-items';
export * from './service-items-integration';
export * from './sessions';
export * from './subcontractor';
export * from './system';
export * from './users';
export * from './crews';
export * from './dead-lead-reasons';
export * from './lead-sources';
export * from './project-invoice-integration';
export * from './global-targets';
export * from './user-targets';
export * from './subscriptions';
export { SchemaFactory };
