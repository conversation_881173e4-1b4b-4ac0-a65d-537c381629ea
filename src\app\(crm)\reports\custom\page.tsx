'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { ArrowLeft, Plus, Save, Download, Share, Filter, BarChart3, <PERSON><PERSON><PERSON>, <PERSON>Chart, FileText, Settings } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ReportBuilder } from '@/lib/reports/page/report-builder-simple';

interface Report {
  id: string;
  name: string;
  description: string;
  type: 'financial' | 'operational' | 'sales' | 'custom';
  lastModified: string;
  createdBy: string;
  isPublic: boolean;
  recordCount: number;
}

export default function CustomReportsPage() {
  const [activeModal, setActiveModal] = useState<string | null>(null);
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const [reports, setReports] = useState<Report[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load reports on component mount
  useEffect(() => {
    loadReports();
  }, []);

  const loadReports = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/custom-reports');
      if (response.ok) {
        const reportsData = await response.json();
        setReports(reportsData);
      } else {
        console.error('Failed to load reports');
      }
    } catch (error) {
      console.error('Error loading reports:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'financial': return 'bg-green-100 text-green-800';
      case 'operational': return 'bg-blue-100 text-blue-800';
      case 'sales': return 'bg-purple-100 text-purple-800';
      case 'custom': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const openReportBuilder = (report: Report) => {
    setSelectedReport(report);
    setActiveModal('report-builder');
  };

  const closeModal = () => {
    setActiveModal(null);
    setSelectedReport(null);
  };

  const handleReportSave = (savedReport: Report) => {
    // Update the reports list with the saved report
    setReports(prevReports => {
      const existingIndex = prevReports.findIndex(r => r.id === savedReport.id);
      if (existingIndex >= 0) {
        // Update existing report
        const updatedReports = [...prevReports];
        updatedReports[existingIndex] = savedReport;
        return updatedReports;
      } else {
        // Add new report
        return [savedReport, ...prevReports];
      }
    });
  };

  const createNewReport = () => {
    const newReport: Report = {
      id: 'new',
      name: 'New Custom Report',
      description: 'Build your custom report with advanced filtering and grouping',
      type: 'custom',
      lastModified: new Date().toLocaleString(),
      createdBy: 'Current User',
      isPublic: false,
      recordCount: 0
    };
    openReportBuilder(newReport);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/report">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Reports
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Custom Report Builder</h1>
            <p className="text-muted-foreground">Create professional reports with style interface</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Share className="h-4 w-4 mr-2" />
            Share
          </Button>
          <Button size="sm" onClick={createNewReport}>
            <Plus className="h-4 w-4 mr-2" />
            New Report
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="all" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="all">All Reports</TabsTrigger>
          <TabsTrigger value="financial">Financial</TabsTrigger>
          <TabsTrigger value="operational">Operational</TabsTrigger>
          <TabsTrigger value="sales">Sales</TabsTrigger>
          <TabsTrigger value="custom">Custom</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-6">
          {isLoading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading reports...</p>
            </div>
          ) : reports.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {reports.map(report => (
                <Card key={report.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <FileText className="h-5 w-5 text-blue-600" />
                        <div>
                          <CardTitle className="text-lg font-medium text-gray-900">
                            {report.name}
                          </CardTitle>
                          <Badge className={`text-xs mt-1 ${getTypeColor(report.type)}`}>
                            {report.type.charAt(0).toUpperCase() + report.type.slice(1)}
                          </Badge>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openReportBuilder(report)}
                      >
                        <Settings className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-600 mb-4">{report.description}</p>

                    <div className="space-y-2 text-xs text-gray-500">
                      <div className="flex justify-between">
                        <span>Records:</span>
                        <span className="font-medium">{report.recordCount.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Created by:</span>
                        <span className="font-medium">{report.createdBy}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Last modified:</span>
                        <span className="font-medium">{report.lastModified}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Visibility:</span>
                        <Badge variant={report.isPublic ? 'default' : 'secondary'} className="text-xs">
                          {report.isPublic ? 'Public' : 'Private'}
                        </Badge>
                      </div>
                    </div>

                    <div className="mt-4 flex gap-2">
                      <Button
                        size="sm"
                        className="flex-1"
                        onClick={() => openReportBuilder(report)}
                      >
                        Edit Report
                      </Button>
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Custom Reports</h3>
              <p className="text-gray-600 mb-6">Create your first custom report to get started</p>
              <Button onClick={createNewReport}>
                <Plus className="h-4 w-4 mr-2" />
                Create New Report
              </Button>
            </div>
          )}
        </TabsContent>

        {/* Other tab contents filter the reports by type */}
        <TabsContent value="financial">
          {reports.filter(r => r.type === 'financial').length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {reports.filter(r => r.type === 'financial').map(report => (
                <Card key={report.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                  {/* Same card structure as above */}
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <FileText className="h-5 w-5 text-blue-600" />
                        <div>
                          <CardTitle className="text-lg font-medium text-gray-900">
                            {report.name}
                          </CardTitle>
                          <Badge className={`text-xs mt-1 ${getTypeColor(report.type)}`}>
                            {report.type.charAt(0).toUpperCase() + report.type.slice(1)}
                          </Badge>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openReportBuilder(report)}
                      >
                        <Settings className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-gray-600 mb-4">{report.description}</p>
                    <div className="mt-4 flex gap-2">
                      <Button
                        size="sm"
                        className="flex-1"
                        onClick={() => openReportBuilder(report)}
                      >
                        Edit Report
                      </Button>
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <BarChart3 className="h-16 w-16 text-green-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Financial Reports</h3>
              <p className="text-gray-600 mb-6">Create financial reports to track revenue, expenses, and profitability</p>
              <Button onClick={createNewReport}>
                <Plus className="h-4 w-4 mr-2" />
                Create Financial Report
              </Button>
            </div>
          )}
        </TabsContent>

        <TabsContent value="operational">
          {reports.filter(r => r.type === 'operational').length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {reports.filter(r => r.type === 'operational').map(report => (
                <Card key={report.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                  {/* Same card structure */}
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Settings className="h-16 w-16 text-blue-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Operational Reports</h3>
              <p className="text-gray-600 mb-6">Create operational reports to track job progress, milestones, and efficiency</p>
              <Button onClick={createNewReport}>
                <Plus className="h-4 w-4 mr-2" />
                Create Operational Report
              </Button>
            </div>
          )}
        </TabsContent>

        <TabsContent value="sales">
          {reports.filter(r => r.type === 'sales').length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {reports.filter(r => r.type === 'sales').map(report => (
                <Card key={report.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                  {/* Same card structure */}
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <BarChart3 className="h-16 w-16 text-purple-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Sales Reports</h3>
              <p className="text-gray-600 mb-6">Create sales reports to track leads, conversions, and performance</p>
              <Button onClick={createNewReport}>
                <Plus className="h-4 w-4 mr-2" />
                Create Sales Report
              </Button>
            </div>
          )}
        </TabsContent>

        <TabsContent value="custom">
          {reports.filter(r => r.type === 'custom').length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {reports.filter(r => r.type === 'custom').map(report => (
                <Card key={report.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                  {/* Same card structure */}
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Custom Reports</h3>
              <p className="text-gray-600 mb-6">Create custom reports with advanced filtering and grouping</p>
              <Button onClick={createNewReport}>
                <Plus className="h-4 w-4 mr-2" />
                Create Custom Report
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <div className="mt-12">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={createNewReport}>
            <CardContent className="p-6 text-center">
              <BarChart3 className="h-8 w-8 text-blue-600 mx-auto mb-3" />
              <h3 className="font-medium text-gray-900 mb-2">Create Custom Report</h3>
              <p className="text-sm text-gray-600">Build a report from scratch with style interface</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <FileText className="h-8 w-8 text-green-600 mx-auto mb-3" />
              <h3 className="font-medium text-gray-900 mb-2">Import Template</h3>
              <p className="text-sm text-gray-600">Start with a pre-built template and customize as needed</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6 text-center">
              <Share className="h-8 w-8 text-purple-600 mx-auto mb-3" />
              <h3 className="font-medium text-gray-900 mb-2">Scheduled Reports</h3>
              <p className="text-sm text-gray-600">Set up automated report generation and delivery</p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Modals */}
      {activeModal === 'report-builder' && selectedReport && (
        <ReportBuilder
          reportTitle={selectedReport.name}
          reportId={selectedReport.id}
          reportData={[]}
          onClose={closeModal}
          onSave={handleReportSave}
        />
      )}
    </div>
  );
}


