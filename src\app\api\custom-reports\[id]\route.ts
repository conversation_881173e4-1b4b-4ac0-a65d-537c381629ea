import { NextRequest, NextResponse } from 'next/server';
import { validateRequest } from '@/server';
import { getCustomReportModel } from '@/schemas/custom-reports';
import mongoose from 'mongoose';

// GET - Fetch a specific custom report
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user, account } = await validateRequest();
    
    if (!user || !account) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ error: 'Invalid report ID' }, { status: 400 });
    }

    const CustomReportModel = await getCustomReportModel();
    
    const report = await CustomReportModel.findOne({ 
      _id: params.id, 
      account: account._id 
    })
      .populate('createdBy', 'firstName lastName email')
      .populate('modifiedBy', 'firstName lastName email')
      .lean();

    if (!report) {
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }

    // Transform the data to match the frontend interface
    const transformedReport = {
      id: report._id.toString(),
      name: report.name,
      description: report.description || '',
      type: report.type,
      lastModified: report.modified.toLocaleString(),
      createdBy: report.createdBy ? 
        `${report.createdBy.firstName} ${report.createdBy.lastName}` : 
        'Unknown User',
      isPublic: report.isPublic,
      recordCount: 0,
      columns: report.columns,
      filters: report.filters,
      calculations: report.calculations
    };

    return NextResponse.json(transformedReport);
  } catch (error) {
    console.error('Error fetching custom report:', error);
    return NextResponse.json({ error: 'Failed to fetch report' }, { status: 500 });
  }
}

// PUT - Update a custom report
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user, account } = await validateRequest();
    
    if (!user || !account) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ error: 'Invalid report ID' }, { status: 400 });
    }

    const data = await request.json();
    const CustomReportModel = await getCustomReportModel();

    const updatedReport = await CustomReportModel.findOneAndUpdate(
      { _id: params.id, account: account._id },
      {
        name: data.name,
        description: data.description || '',
        type: data.type || 'custom',
        columns: data.columns || [],
        filters: data.filters || [],
        calculations: data.calculations || [],
        isPublic: data.isPublic || false,
        modifiedBy: user._id,
        modified: new Date()
      },
      { new: true }
    )
      .populate('createdBy', 'firstName lastName email')
      .populate('modifiedBy', 'firstName lastName email');

    if (!updatedReport) {
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }

    // Transform the response to match frontend interface
    const transformedReport = {
      id: updatedReport._id.toString(),
      name: updatedReport.name,
      description: updatedReport.description || '',
      type: updatedReport.type,
      lastModified: updatedReport.modified.toLocaleString(),
      createdBy: updatedReport.createdBy ? 
        `${updatedReport.createdBy.firstName} ${updatedReport.createdBy.lastName}` : 
        'Unknown User',
      isPublic: updatedReport.isPublic,
      recordCount: 0,
      columns: updatedReport.columns,
      filters: updatedReport.filters,
      calculations: updatedReport.calculations
    };

    return NextResponse.json(transformedReport);
  } catch (error) {
    console.error('Error updating custom report:', error);
    return NextResponse.json({ error: 'Failed to update report' }, { status: 500 });
  }
}

// DELETE - Delete a custom report
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user, account } = await validateRequest();
    
    if (!user || !account) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!mongoose.Types.ObjectId.isValid(params.id)) {
      return NextResponse.json({ error: 'Invalid report ID' }, { status: 400 });
    }

    const CustomReportModel = await getCustomReportModel();

    const deletedReport = await CustomReportModel.findOneAndDelete({
      _id: params.id,
      account: account._id
    });

    if (!deletedReport) {
      return NextResponse.json({ error: 'Report not found' }, { status: 404 });
    }

    return NextResponse.json({ message: 'Report deleted successfully' });
  } catch (error) {
    console.error('Error deleting custom report:', error);
    return NextResponse.json({ error: 'Failed to delete report' }, { status: 500 });
  }
}
