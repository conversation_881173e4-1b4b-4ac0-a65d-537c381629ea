import { Schema } from 'mongoose';

import { SCHEMAS } from '@/server';
import { SchemaFactory } from '@/schemas/schema-factory';
import type { CustomReport } from './types';

const ColumnOptionSchema = new Schema({
  id: { type: String, required: true },
  label: { type: String, required: true },
  category: { type: String, required: true },
  selected: { type: Boolean, default: false },
  description: { type: String }
}, { _id: false });

const FilterSchema = new Schema({
  id: { type: String, required: true },
  field: { type: String, required: true },
  operator: { type: String, required: true },
  value: { type: String, required: true },
  label: { type: String, required: true }
}, { _id: false });

const GroupCalculationSchema = new Schema({
  id: { type: String, required: true },
  operation: { type: String, required: true },
  field: { type: String, required: true },
  label: { type: String, required: true }
}, { _id: false });

export const CustomReportSchema = new SchemaFactory<CustomReport>(
  SCHEMAS.CUSTOM_REPORT,
  {
    _id: { type: Schema.Types.ObjectId, auto: true, required: true },
    account: { type: Schema.Types.ObjectId, ref: SCHEMAS.ACCOUNT, required: true, index: true },
    name: { type: String, required: true, trim: true },
    description: { type: String, trim: true },
    type: {
      type: String,
      enum: ['financial', 'operational', 'sales', 'custom'],
      default: 'custom',
      index: true
    },
    columns: { type: [ColumnOptionSchema], default: [] },
    filters: { type: [FilterSchema], default: [] },
    calculations: { type: [GroupCalculationSchema], default: [] },
    isPublic: { type: Boolean, default: false, index: true },
    createdBy: { type: Schema.Types.ObjectId, ref: SCHEMAS.USER, required: true },
    modifiedBy: { type: Schema.Types.ObjectId, ref: SCHEMAS.USER, required: true },
    created: { type: Date, default: Date.now },
    modified: { type: Date, default: Date.now }
  },
  {
    collection: 'custom-reports',
    insertIntoSearchCollection: true,
  },
);

// Add middleware to update modified date
function CustomReportMiddleware(schema: Schema<CustomReport>) {
  schema.pre('save', function(next) {
    this.modified = new Date();
    next();
  });
}

CustomReportSchema.middleware(CustomReportMiddleware);
