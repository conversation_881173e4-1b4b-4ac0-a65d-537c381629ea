export enum SCHEMAS {
  ACCOUNT = 'Account',
  CALENDAR_EVENT = 'CalendarEvent',
  CUSTOMER = 'Customer',
  CUSTOMER_INTEGRATION = 'CustomerIntegration',
  CUSTOM_REPORT = 'CustomReport',
  DEAD_LEAD_REASON = 'DeadLeadReason',
  FILE = 'File',
  INSURANCE = 'InsuranceCompany',
  LOCATION = 'Location',
  LEAD_SOURCE = 'LeadSource',
  NOTIFICATION = 'Notification',
  PROJECT = 'Project',
  QUOTE_TEMPLATE = 'QuoteTemplate',
  SCHEDULED_TASKS = 'ScheduledTasks',
  SEARCH = 'Search',
  SERVICE_ITEM = 'ServiceItem',
  SERVICE_ITEM_INTEGRATION = 'ServiceItemIntegration',
  SESSION = 'Session',
  SUBCONTRACTOR = 'Subcontractor',
  CREW = 'Crew',
  SYSTEM = 'System',
  USER = 'User',
  PROJECT_INVOICE_INTEGRATION = 'ProjectInvoiceIntegration',
  GLOBAL_TARGET = 'GlobalTarget',
  USER_TARGET = 'UserTarget',
  SUBSCRIPTION = 'Subscription',
}
