'use client';

import { Bar<PERSON><PERSON>3, Filter, <PERSON><PERSON><PERSON>, Calculator, X } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

interface ReportBuilderProps {
  reportTitle: string;
  reportData: Record<string, unknown>[];
  onClose?: () => void;
}

export function ReportBuilder({ reportTitle, onClose }: ReportBuilderProps) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl h-4/5 flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-900">{reportTitle}</h2>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 p-6 overflow-auto">
          <div className="text-center py-12">
            <BarChart3 className="h-16 w-16 text-blue-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Report Builder</h3>
            <p className="text-gray-600 mb-6">Professional report configuration interface</p>
            
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
              <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer">
                <Settings className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <h4 className="font-medium text-sm">Columns</h4>
                <p className="text-xs text-gray-500">Configure columns</p>
              </Card>
              
              <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer">
                <Filter className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <h4 className="font-medium text-sm">Filters</h4>
                <p className="text-xs text-gray-500">Add filters</p>
              </Card>
              
              <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer">
                <Calculator className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                <h4 className="font-medium text-sm">Calculations</h4>
                <p className="text-xs text-gray-500">Group calculations</p>
              </Card>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 flex justify-end gap-3">
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button>Save Report</Button>
        </div>
      </div>
    </div>
  );
}
